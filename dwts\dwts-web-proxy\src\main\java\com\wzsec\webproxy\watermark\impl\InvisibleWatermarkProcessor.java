package com.wzsec.webproxy.watermark.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.AbstractWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 不可见暗水印处理器
 * 使用零宽字符技术在API响应中嵌入不可见水印，支持溯源追踪
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class InvisibleWatermarkProcessor extends AbstractWatermarkProcessor {

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 零宽字符定义
    private static final char ZERO_WIDTH_SPACE = '\u200B';           // 零宽空格
    private static final char ZERO_WIDTH_NON_JOINER = '\u200C';      // 零宽非连接符
    private static final char ZERO_WIDTH_JOINER = '\u200D';          // 零宽连接符
    private static final char WORD_JOINER = '\u2060';                // 词连接符
    private static final char INVISIBLE_SEPARATOR = '\u2062';        // 不可见分隔符

    // 编码映射表
    private static final Map<String, Character> ENCODING_MAP = new HashMap<>();

    static {
        ENCODING_MAP.put("00", ZERO_WIDTH_SPACE);
        ENCODING_MAP.put("01", ZERO_WIDTH_NON_JOINER);
        ENCODING_MAP.put("10", ZERO_WIDTH_JOINER);
        ENCODING_MAP.put("11", WORD_JOINER);
    }

    // 解码映射表
    private static final Map<Character, String> DECODING_MAP = new HashMap<>();

    static {
        DECODING_MAP.put(ZERO_WIDTH_SPACE, "00");
        DECODING_MAP.put(ZERO_WIDTH_NON_JOINER, "01");
        DECODING_MAP.put(ZERO_WIDTH_JOINER, "10");
        DECODING_MAP.put(WORD_JOINER, "11");
    }

    @Override
    public byte[] processWatermark(byte[] content, String contentType,
                                   HttpServletRequest request, WebProxyConfig config) {
        try {
            // 检查是否启用API水印（暗水印）
            if (!Boolean.TRUE.equals(config.getEnableApiWatermark())) {
                return content;
            }

            String contentString = safeToString(content);
            if (contentString.trim().isEmpty()) {
                return content;
            }

            // 打印加注前的原始内容
            log.info("=== API水印（暗水印）加注前 ===");
            log.info("内容类型: {}", contentType);
            log.info("原始内容长度: {} bytes", contentString.length());
            log.info("原始内容: {}", formatContentForLog(contentString));

            // 生成水印信息
            WatermarkInfo watermarkInfo = generateWatermarkInfo(request, config);
            log.info("水印信息: 用户={}, IP={}, 时间={}, 会话={}",
                    watermarkInfo.getUserId(), watermarkInfo.getIpAddress(),
                    watermarkInfo.getTimestamp(), watermarkInfo.getSessionId());

            // 根据内容类型处理
            String watermarkedContent;
            if (isJsonContent(contentType)) {
                watermarkedContent = processJsonContent(contentString, watermarkInfo);
            } else if (isXmlContent(contentType)) {
                watermarkedContent = processXmlContent(contentString, watermarkInfo);
            } else {
                watermarkedContent = processTextContent(contentString, watermarkInfo);
            }

            // 打印加注后的结果
            log.info("=== API水印（暗水印）加注后 ===");
            log.info("处理后内容长度: {} bytes", watermarkedContent.length());
            log.info("长度变化: {} bytes", watermarkedContent.length() - contentString.length());
            log.info("处理后内容: {}", formatContentForLog(watermarkedContent));

            // 验证数据结构完整性
            if (isJsonContent(contentType)) {
                verifyJsonStructure(contentString, watermarkedContent);
            } else if (isXmlContent(contentType)) {
                verifyXmlStructure(contentString, watermarkedContent);
            }

            log.info("API水印（暗水印）加注完成 - 数据结构保持完整");

            return safeToBytes(watermarkedContent);

        } catch (Exception e) {
            log.error("暗水印处理失败", e);
            return content; // 失败时返回原内容
        }
    }

    /**
     * 处理JSON内容
     */
    private String processJsonContent(String jsonString, WatermarkInfo watermarkInfo) throws Exception {
        JsonNode rootNode = objectMapper.readTree(jsonString);
        JsonNode watermarkedNode = injectInvisibleWatermarkToJson(rootNode, watermarkInfo);
        return objectMapper.writeValueAsString(watermarkedNode);
    }

    /**
     * 向JSON中注入不可见水印
     */
    private JsonNode injectInvisibleWatermarkToJson(JsonNode node, WatermarkInfo watermarkInfo) {
        if (node.isObject()) {
            return processObjectNode((ObjectNode) node, watermarkInfo);
        } else if (node.isArray()) {
            return processArrayNode((ArrayNode) node, watermarkInfo);
        } else if (node.isTextual()) {
            return processTextNode((TextNode) node, watermarkInfo);
        }
        return node;
    }

    /**
     * 处理JSON对象节点
     */
    private ObjectNode processObjectNode(ObjectNode objectNode, WatermarkInfo watermarkInfo) {
        ObjectNode result = objectNode.deepCopy();

        // 在字符串字段中嵌入暗水印
        result.fields().forEachRemaining(entry -> {
            JsonNode value = entry.getValue();
            if (value.isTextual()) {
                String originalText = value.asText();
                if (shouldEmbedWatermark(entry.getKey(), originalText)) {
                    String watermarkedText = embedInvisibleWatermark(originalText, watermarkInfo);
                    result.put(entry.getKey(), watermarkedText);
                    log.debug("字段 '{}' 水印嵌入: '{}' -> '{}' (长度: {} -> {})",
                             entry.getKey(), originalText, watermarkedText,
                             originalText.length(), watermarkedText.length());
                }
            } else if (value.isObject() || value.isArray()) {
                result.set(entry.getKey(), injectInvisibleWatermarkToJson(value, watermarkInfo));
            }
        });

        return result;
    }

    /**
     * 处理JSON数组节点
     */
    private ArrayNode processArrayNode(ArrayNode arrayNode, WatermarkInfo watermarkInfo) {
        ArrayNode result = arrayNode.deepCopy();

        for (int i = 0; i < result.size(); i++) {
            JsonNode element = result.get(i);
            if (element.isTextual()) {
                String originalText = element.asText();
                String watermarkedText = embedInvisibleWatermark(originalText, watermarkInfo);
                result.set(i, new TextNode(watermarkedText));
                log.debug("数组元素 [{}] 水印嵌入: '{}' -> '{}' (长度: {} -> {})",
                         i, originalText, watermarkedText,
                         originalText.length(), watermarkedText.length());
            } else if (element.isObject() || element.isArray()) {
                result.set(i, injectInvisibleWatermarkToJson(element, watermarkInfo));
            }
        }

        return result;
    }

    /**
     * 处理文本节点
     */
    private TextNode processTextNode(TextNode textNode, WatermarkInfo watermarkInfo) {
        String originalText = textNode.asText();
        String watermarkedText = embedInvisibleWatermark(originalText, watermarkInfo);
        return new TextNode(watermarkedText);
    }

    /**
     * 处理XML内容
     */
    private String processXmlContent(String xmlString, WatermarkInfo watermarkInfo) {
        // 在XML文本节点中嵌入暗水印
        Pattern pattern = Pattern.compile(">([^<]+)<");
        Matcher matcher = pattern.matcher(xmlString);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String textContent = matcher.group(1);
            if (textContent.trim().length() > 0 && !textContent.trim().matches("^[\\d\\s.,]+$")) {
                String watermarkedText = embedInvisibleWatermark(textContent, watermarkInfo);
                matcher.appendReplacement(result, ">" + Matcher.quoteReplacement(watermarkedText) + "<");
            } else {
                matcher.appendReplacement(result, matcher.group(0));
            }
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 处理纯文本内容
     */
    private String processTextContent(String textContent, WatermarkInfo watermarkInfo) {
        return embedInvisibleWatermark(textContent, watermarkInfo);
    }

    /**
     * 在文本中嵌入不可见水印
     */
    private String embedInvisibleWatermark(String originalText, WatermarkInfo watermarkInfo) {
        if (originalText == null || originalText.trim().isEmpty()) {
            return originalText;
        }

        try {
            // 生成水印编码
            String watermarkCode = encodeWatermarkInfo(watermarkInfo);

            // 将水印编码转换为零宽字符
            String invisibleWatermark = encodeToZeroWidthChars(watermarkCode);

            // 智能嵌入位置选择
            List<Integer> embedPositions = selectEmbedPositions(originalText, invisibleWatermark.length());

            StringBuilder result = new StringBuilder(originalText);
            int offset = 0;

            for (int i = 0; i < invisibleWatermark.length() && i < embedPositions.size(); i++) {
                int position = embedPositions.get(i) + offset;
                char watermarkChar = invisibleWatermark.charAt(i);
                result.insert(position, watermarkChar);
                offset++;
            }

            return result.toString();

        } catch (Exception e) {
            log.warn("嵌入暗水印失败: {}", e.getMessage());
            return originalText;
        }
    }

    /**
     * 编码水印信息
     */
    private String encodeWatermarkInfo(WatermarkInfo watermarkInfo) throws Exception {
        // 创建紧凑的水印数据
        String watermarkData = String.format("%s|%s|%d|%s",
                watermarkInfo.getUserId(),
                watermarkInfo.getIpAddress(),
                watermarkInfo.getTimestamp(),
                watermarkInfo.getSessionId().substring(0, Math.min(8, watermarkInfo.getSessionId().length()))
        );

        // 计算校验和
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hash = md.digest(watermarkData.getBytes(StandardCharsets.UTF_8));
        String checksum = Base64.getEncoder().encodeToString(hash).substring(0, 4);

        return watermarkData + "|" + checksum;
    }

    /**
     * 将字符串编码为零宽字符
     */
    private String encodeToZeroWidthChars(String data) {
        StringBuilder result = new StringBuilder();

        // 添加起始标记
        result.append(INVISIBLE_SEPARATOR);

        for (char c : data.toCharArray()) {
            String binary = String.format("%8s", Integer.toBinaryString(c)).replace(' ', '0');

            // 每2位二进制转换为一个零宽字符
            for (int i = 0; i < binary.length(); i += 2) {
                String bits = binary.substring(i, Math.min(i + 2, binary.length()));
                if (bits.length() == 1) bits += "0"; // 补齐

                Character zeroWidthChar = ENCODING_MAP.get(bits);
                if (zeroWidthChar != null) {
                    result.append(zeroWidthChar);
                }
            }
        }

        // 添加结束标记
        result.append(INVISIBLE_SEPARATOR);

        return result.toString();
    }

    /**
     * 选择嵌入位置
     */
    private List<Integer> selectEmbedPositions(String text, int watermarkLength) {
        List<Integer> positions = new ArrayList<>();
        Random random = new Random();

        // 在单词边界和标点符号后选择位置
        for (int i = 0; i < text.length() && positions.size() < watermarkLength; i++) {
            char c = text.charAt(i);
            if (Character.isWhitespace(c) || isPunctuation(c)) {
                if (i + 1 < text.length()) {
                    positions.add(i + 1);
                }
            }
        }

        // 如果位置不够，随机选择
        while (positions.size() < watermarkLength && positions.size() < text.length()) {
            int pos = random.nextInt(text.length());
            if (!positions.contains(pos)) {
                positions.add(pos);
            }
        }

        Collections.sort(positions);
        return positions;
    }

    /**
     * 判断是否应该嵌入水印
     */
    private boolean shouldEmbedWatermark(String fieldName, String value) {
        // 跳过敏感字段
        if (fieldName.toLowerCase().contains("password") ||
                fieldName.toLowerCase().contains("token") ||
                fieldName.toLowerCase().contains("key")) {
            log.debug("跳过敏感字段: {}", fieldName);
            return false;
        }

        // 跳过过短的文本（降低限制到3个字符）
        if (value.length() < 3) {
            log.debug("跳过过短文本: '{}' (长度: {})", value, value.length());
            return false;
        }

        // 跳过纯数字ID字段（但允许其他数字内容）
        if (fieldName.toLowerCase().equals("id") && value.matches("^\\d+$")) {
            log.debug("跳过数字ID字段: {} = {}", fieldName, value);
            return false;
        }

        // 允许所有其他文本内容（包括中文、英文、数字、混合内容）
        log.debug("允许嵌入水印: {} = '{}' (长度: {})", fieldName, value, value.length());
        return true;
    }

    /**
     * 判断是否为标点符号
     */
    private boolean isPunctuation(char c) {
        return ".,;:!?()[]{}\"'".indexOf(c) >= 0;
    }

    /**
     * 判断是否为JSON内容
     */
    private boolean isJsonContent(String contentType) {
        return contentType != null &&
                (contentType.toLowerCase().contains("application/json") ||
                        contentType.toLowerCase().contains("text/json"));
    }

    /**
     * 判断是否为XML内容
     */
    private boolean isXmlContent(String contentType) {
        return contentType != null &&
                (contentType.toLowerCase().contains("application/xml") ||
                        contentType.toLowerCase().contains("text/xml"));
    }

    /**
     * 生成水印信息
     */
    private WatermarkInfo generateWatermarkInfo(HttpServletRequest request, WebProxyConfig config) {
        return WatermarkInfo.builder()
                .userId(getCurrentUser(request))
                .ipAddress(getClientIpAddress(request))
                .timestamp(System.currentTimeMillis())
                .sessionId(request.getSession().getId())
                .proxyName(config.getProxyName())
                .requestPath(request.getRequestURI())
                .build();
    }

    @Override
    public boolean canHandle(String contentType) {
        // 只处理数据接口的内容类型（JSON、XML、纯文本数据）
        return isJsonContent(contentType) ||
               isXmlContent(contentType) ||
               isTextDataContent(contentType);
    }

    /**
     * 判断是否为文本数据内容（排除HTML等页面内容）
     */
    private boolean isTextDataContent(String contentType) {
        if (contentType == null) {
            return false;
        }

        String lowerContentType = contentType.toLowerCase();

        // 包含的数据类型
        return lowerContentType.contains("text/plain") ||
               lowerContentType.contains("text/csv") ||
               lowerContentType.contains("application/csv") ||
               lowerContentType.contains("application/x-ndjson") ||
               lowerContentType.contains("application/jsonl");
    }

    @Override
    public String getProcessorName() {
        return "InvisibleWatermarkProcessor";
    }

    @Override
    public String getWatermarkType() {
        return "INVISIBLE";
    }

    /**
     * 格式化内容用于日志打印（限制长度避免日志过长）
     */
    private String formatContentForLog(String content) {
        if (content == null) {
            return "null";
        }

        // 限制打印长度，避免日志过长
        int maxLength = 500;
        if (content.length() <= maxLength) {
            return content;
        }

        return content.substring(0, maxLength) + "... (总长度: " + content.length() + " 字符)";
    }

    /**
     * 验证JSON数据结构完整性
     */
    private void verifyJsonStructure(String original, String watermarked) {
        try {
            JsonNode originalNode = objectMapper.readTree(original);
            JsonNode watermarkedNode = objectMapper.readTree(watermarked);

            // 验证根节点类型是否一致
            if (originalNode.getNodeType() != watermarkedNode.getNodeType()) {
                log.warn("JSON结构验证警告: 根节点类型不一致");
                return;
            }

            // 验证字段数量（对于对象类型）
            if (originalNode.isObject()) {
                int originalFieldCount = originalNode.size();
                int watermarkedFieldCount = watermarkedNode.size();
                if (originalFieldCount != watermarkedFieldCount) {
                    log.warn("JSON结构验证警告: 字段数量不一致 - 原始: {}, 处理后: {}",
                            originalFieldCount, watermarkedFieldCount);
                }
            }

            log.debug("JSON数据结构验证通过");

        } catch (Exception e) {
            log.warn("JSON结构验证失败: {}", e.getMessage());
        }
    }

    /**
     * 验证XML数据结构完整性
     */
    private void verifyXmlStructure(String original, String watermarked) {
        try {
            // 简单验证：检查XML标签数量是否一致
            int originalTagCount = countXmlTags(original);
            int watermarkedTagCount = countXmlTags(watermarked);

            if (originalTagCount != watermarkedTagCount) {
                log.warn("XML结构验证警告: 标签数量不一致 - 原始: {}, 处理后: {}",
                        originalTagCount, watermarkedTagCount);
            } else {
                log.debug("XML数据结构验证通过");
            }

        } catch (Exception e) {
            log.warn("XML结构验证失败: {}", e.getMessage());
        }
    }

    /**
     * 计算XML标签数量
     */
    private int countXmlTags(String xml) {
        if (xml == null) return 0;

        int count = 0;
        int index = 0;
        while ((index = xml.indexOf('<', index)) != -1) {
            if (index + 1 < xml.length() && xml.charAt(index + 1) != '!') {
                count++;
            }
            index++;
        }
        return count;
    }

    /**
     * 水印信息数据类
     */
    public static class WatermarkInfo {
        private String userId;
        private String ipAddress;
        private long timestamp;
        private String sessionId;
        private String proxyName;
        private String requestPath;

        public static WatermarkInfoBuilder builder() {
            return new WatermarkInfoBuilder();
        }

        // Getters
        public String getUserId() {
            return userId;
        }

        public String getIpAddress() {
            return ipAddress;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public String getSessionId() {
            return sessionId;
        }

        public String getProxyName() {
            return proxyName;
        }

        public String getRequestPath() {
            return requestPath;
        }

        public static class WatermarkInfoBuilder {
            private WatermarkInfo info = new WatermarkInfo();

            public WatermarkInfoBuilder userId(String userId) {
                info.userId = userId;
                return this;
            }

            public WatermarkInfoBuilder ipAddress(String ipAddress) {
                info.ipAddress = ipAddress;
                return this;
            }

            public WatermarkInfoBuilder timestamp(long timestamp) {
                info.timestamp = timestamp;
                return this;
            }

            public WatermarkInfoBuilder sessionId(String sessionId) {
                info.sessionId = sessionId;
                return this;
            }

            public WatermarkInfoBuilder proxyName(String proxyName) {
                info.proxyName = proxyName;
                return this;
            }

            public WatermarkInfoBuilder requestPath(String requestPath) {
                info.requestPath = requestPath;
                return this;
            }

            public WatermarkInfo build() {
                return info;
            }
        }
    }
}
