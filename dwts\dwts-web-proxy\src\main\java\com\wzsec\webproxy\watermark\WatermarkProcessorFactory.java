package com.wzsec.webproxy.watermark;

import com.wzsec.webproxy.watermark.impl.HtmlWatermarkProcessor;
import com.wzsec.webproxy.watermark.impl.InvisibleWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 水印处理器工厂
 * 只保留页面水印和不可见字符水印处理器
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Component
public class WatermarkProcessorFactory {

    @Autowired
    private HtmlWatermarkProcessor htmlWatermarkProcessor;

    @Autowired
    private InvisibleWatermarkProcessor invisibleWatermarkProcessor;

    private List<WatermarkProcessor> processors;

    @PostConstruct
    public void initProcessors() {
        processors = new ArrayList<>();
        processors.add(htmlWatermarkProcessor);
        processors.add(invisibleWatermarkProcessor);

        log.info("初始化水印处理器完成，共{}个处理器", processors.size());
        for (WatermarkProcessor processor : processors) {
            log.info("- {}: {}", processor.getProcessorName(), processor.getWatermarkType());
        }
    }

    /**
     * 根据内容类型获取合适的水印处理器
     *
     * @param contentType 内容类型
     * @return 水印处理器
     */
    public WatermarkProcessor getProcessor(String contentType) {
        if (contentType == null) {
            log.debug("内容类型为空，不进行水印处理");
            return null;
        }

        // 遍历所有处理器，找到第一个能处理该内容类型的处理器
        for (WatermarkProcessor processor : processors) {
            if (processor.canHandle(contentType)) {
                log.debug("内容类型[{}]使用处理器[{}]", contentType, processor.getProcessorName());
                return processor;
            }
        }

        // 如果没有找到合适的处理器，说明不需要水印处理，返回null
        log.debug("内容类型[{}]不需要水印处理，直接放行", contentType);
        return null;
    }

    /**
     * 根据内容类型和配置获取合适的水印处理器
     *
     * @param contentType 内容类型
     * @param config 代理配置
     * @return 水印处理器
     */
    public WatermarkProcessor getProcessor(String contentType, com.wzsec.webproxy.domain.WebProxyConfig config) {
        if (contentType == null) {
            log.debug("内容类型为空，不进行水印处理");
            return null;
        }

        // 检查是否为HTML内容，如果是且启用了页面水印，使用HTML处理器
        if (isHtmlContent(contentType) && Boolean.TRUE.equals(config.getEnablePageWatermark())) {
            log.debug("内容类型[{}]使用页面水印处理器", contentType);
            return htmlWatermarkProcessor;
        }

        // 检查是否为数据接口内容，如果是且启用了API水印，使用暗水印处理器
        if (Boolean.TRUE.equals(config.getEnableApiWatermark()) && invisibleWatermarkProcessor.canHandle(contentType)) {
            log.debug("内容类型[{}]使用API水印（暗水印）处理器", contentType);
            return invisibleWatermarkProcessor;
        }

        // 其他类型内容直接放行，不进行水印处理
        log.debug("内容类型[{}]不需要水印处理，直接放行", contentType);
        return null;
    }

    /**
     * 判断是否为HTML内容
     */
    private boolean isHtmlContent(String contentType) {
        return contentType != null &&
               (contentType.toLowerCase().contains("text/html") ||
                contentType.toLowerCase().contains("application/xhtml"));
    }

    /**
     * 获取HTML处理器
     */
    public HtmlWatermarkProcessor getHtmlProcessor() {
        return htmlWatermarkProcessor;
    }

    /**
     * 获取不可见水印处理器
     */
    public InvisibleWatermarkProcessor getInvisibleProcessor() {
        return invisibleWatermarkProcessor;
    }

    /**
     * 获取所有处理器
     */
    public List<WatermarkProcessor> getAllProcessors() {
        return new ArrayList<>(processors);
    }

    /**
     * 根据处理器名称获取处理器
     *
     * @param processorName 处理器名称
     * @return 水印处理器，如果未找到返回null
     */
    public WatermarkProcessor getProcessorByName(String processorName) {
        if (processorName == null) {
            return null;
        }

        for (WatermarkProcessor processor : processors) {
            if (processorName.equals(processor.getProcessorName())) {
                return processor;
            }
        }

        return null;
    }

    /**
     * 根据水印类型获取处理器
     *
     * @param watermarkType 水印类型
     * @return 水印处理器，如果未找到返回null
     */
    public WatermarkProcessor getProcessorByType(String watermarkType) {
        if (watermarkType == null) {
            return null;
        }

        for (WatermarkProcessor processor : processors) {
            if (watermarkType.equals(processor.getWatermarkType())) {
                return processor;
            }
        }

        return null;
    }
}
