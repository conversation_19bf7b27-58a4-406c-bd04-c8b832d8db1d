#!/bin/bash

# 水印检测测试脚本
# 用于测试水印检测接口的功能

BASE_URL="http://localhost:9090"
API_URL="$BASE_URL/api/watermark"

echo "=== 水印检测功能测试 ==="
echo "服务地址: $BASE_URL"
echo

# 1. 健康检查
echo "1. 健康检查测试"
echo "请求: GET $API_URL/health"
curl -s -X GET "$API_URL/health" | jq '.' || echo "健康检查失败"
echo
echo "---"
echo

# 2. 测试纯文本检测
echo "2. 纯文本检测测试"
echo "请求: POST $API_URL/detect"
echo "内容: Hello World"
curl -s -X POST "$API_URL/detect" \
  -H "Content-Type: text/plain" \
  -d "Hello World" | jq '.' || echo "纯文本检测失败"
echo
echo "---"
echo

# 3. 测试JSON检测
echo "3. JSON检测测试"
echo "请求: POST $API_URL/detect"
echo "内容: {\"test\": \"data\"}"
curl -s -X POST "$API_URL/detect" \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}' | jq '.' || echo "JSON检测失败"
echo
echo "---"
echo

# 4. 测试包含零宽字符的内容
echo "4. 零宽字符检测测试"
echo "请求: POST $API_URL/detect"
echo "内容: 包含零宽字符的文本"
# 在文本中插入一些零宽字符进行测试
TEST_CONTENT="Hello​‌‍World"  # 包含 \u200B \u200C \u200D
curl -s -X POST "$API_URL/detect" \
  -H "Content-Type: text/plain" \
  -d "$TEST_CONTENT" | jq '.' || echo "零宽字符检测失败"
echo
echo "---"
echo

# 5. 测试XML检测
echo "5. XML检测测试"
echo "请求: POST $API_URL/detect"
echo "内容: XML文档"
curl -s -X POST "$API_URL/detect" \
  -H "Content-Type: application/xml" \
  -d '<?xml version="1.0"?><root><item>test</item></root>' | jq '.' || echo "XML检测失败"
echo
echo "---"
echo

# 6. 测试HTML检测
echo "6. HTML检测测试"
echo "请求: POST $API_URL/detect"
echo "内容: HTML文档"
curl -s -X POST "$API_URL/detect" \
  -H "Content-Type: text/html" \
  -d '<html><head><title>Test</title></head><body>Content</body></html>' | jq '.' || echo "HTML检测失败"
echo
echo "---"
echo

# 7. 测试自动类型识别
echo "7. 自动类型识别测试"
echo "请求: POST $API_URL/detect (不指定Content-Type)"
echo "内容: JSON格式"
curl -s -X POST "$API_URL/detect" \
  -d '{"auto": "detection", "test": true}' | jq '.' || echo "自动识别检测失败"
echo
echo "---"
echo

# 8. 测试空内容
echo "8. 空内容测试"
echo "请求: POST $API_URL/detect"
echo "内容: 空字符串"
curl -s -X POST "$API_URL/detect" \
  -H "Content-Type: text/plain" \
  -d "" | jq '.' || echo "空内容检测失败"
echo
echo "---"
echo

# 9. 测试大内容
echo "9. 大内容测试"
echo "请求: POST $API_URL/detect"
echo "内容: 大JSON对象"
LARGE_JSON='{"data": ["item1", "item2", "item3", "item4", "item5"], "metadata": {"count": 5, "type": "test", "description": "This is a larger JSON object for testing purposes"}}'
curl -s -X POST "$API_URL/detect" \
  -H "Content-Type: application/json" \
  -d "$LARGE_JSON" | jq '.' || echo "大内容检测失败"
echo
echo "---"
echo

echo "=== 测试完成 ==="
echo
echo "如果所有测试都返回了JSON响应，说明接口工作正常。"
echo "检查响应中的 'success' 字段来确认检测是否成功。"
echo "检查 'data.hasWatermark' 字段来确认是否检测到水印。"
echo
echo "注意事项："
echo "1. 确保服务已启动并运行在 $BASE_URL"
echo "2. 如果某些测试失败，检查服务日志获取详细错误信息"
echo "3. 零宽字符测试可能需要特殊的终端支持才能正确显示"
echo
echo "查看服务日志："
echo "tail -f /var/log/dwts-web-proxy/application.log"
echo
echo "或者使用 docker logs（如果使用Docker）："
echo "docker logs -f dwts-web-proxy"
