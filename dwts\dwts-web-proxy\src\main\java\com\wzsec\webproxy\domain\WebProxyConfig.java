package com.wzsec.webproxy.domain;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Web代理配置实体类
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Entity
@Data
@Table(name = "dwts_web_proxy_config")
public class WebProxyConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /** ID */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /** 代理名称 */
    @Column(name = "proxy_name")
    @NotBlank(message = "代理名称不能为空")
    private String proxyName;

    /** 代理端口 */
    @Column(name = "proxy_port")
    @NotNull(message = "代理端口不能为空")
    private Integer proxyPort;

    /** 目标主机地址 */
    @Column(name = "target_host")
    @NotBlank(message = "目标主机地址不能为空")
    private String targetHost;

    /** 目标端口 */
    @Column(name = "target_port")
    @NotNull(message = "目标端口不能为空")
    private Integer targetPort;

    /** 目标协议 (http/https) */
    @Column(name = "target_protocol")
    private String targetProtocol;

    /** 水印文本 */
    @Column(name = "watermark_text")
    private String watermarkText;

    /** 是否启用页面水印 */
    @Column(name = "enable_page_watermark")
    private Boolean enablePageWatermark;

    /** 是否启用API水印（暗水印/不可见字符水印） */
    @Column(name = "enable_api_watermark")
    private Boolean enableApiWatermark;

    /** 暗水印编码强度 */
    @Column(name = "invisible_encoding_strength")
    private String invisibleEncodingStrength;

    /** 暗水印嵌入密度 */
    @Column(name = "invisible_embed_density")
    private Double invisibleEmbedDensity;

    /** API路径模式 (逗号分隔) */
    @Column(name = "api_path_patterns")
    private String apiPathPatterns;

    /** 水印透明度 (0.0-1.0) */
    @Column(name = "watermark_opacity")
    private Double watermarkOpacity;

    /** 水印宽度 */
    @Column(name = "watermark_width")
    private Integer watermarkWidth;

    /** 水印高度 */
    @Column(name = "watermark_height")
    private Integer watermarkHeight;

    /** 水印颜色 */
    @Column(name = "watermark_color")
    private String watermarkColor;

    /** 水印角度 */
    @Column(name = "watermark_angle")
    private Double watermarkAngle;

    /** 是否启用链接重写 */
    @Column(name = "enable_link_rewrite")
    private Boolean enableLinkRewrite;

    /** 是否启用API拦截 */
    @Column(name = "enable_api_intercept")
    private Boolean enableApiIntercept;

    /** 状态 (ACTIVE/INACTIVE) */
    @Column(name = "status")
    private String status;

    /** 备注 */
    @Column(name = "remark")
    private String remark;

    /** 创建用户 */
    @Column(name = "create_user")
    private String createUser;

    /** 创建时间 */
    @Column(name = "create_time")
    @CreationTimestamp
    private Timestamp createTime;

    /** 更新用户 */
    @Column(name = "update_user")
    private String updateUser;

    /** 更新时间 */
    @Column(name = "update_time")
    @UpdateTimestamp
    private Timestamp updateTime;

    /**
     * 获取目标基础URL
     *
     * @return {@code String }
     */
    public String getTargetBaseUrl() {
        if (targetHost == null || targetHost.trim().isEmpty()) {
            throw new IllegalStateException("目标主机地址不能为空");
        }

        String protocol = targetProtocol != null ? targetProtocol : "http";
        Integer port = targetPort != null ? targetPort : ("https".equals(protocol) ? 443 : 80);

        StringBuilder url = new StringBuilder();
        url.append(protocol).append("://").append(targetHost.trim());

        // 只有非标准端口才添加端口号
        if (!"http".equals(protocol) || port != 80) {
            if (!"https".equals(protocol) || port != 443) {
                url.append(":").append(port);
            }
        }

        return url.toString();
    }

    /**
     * 检查配置是否有效
     *
     * @return boolean
     */
    public boolean isValid() {
        return "ACTIVE".equals(status) &&
                proxyPort != null &&
                targetHost != null &&
                targetPort != null;
    }
}
