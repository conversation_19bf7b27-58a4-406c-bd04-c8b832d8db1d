package com.wzsec.webproxy.service;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.repository.WebProxyConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 水印配置管理服务
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Service
public class WatermarkConfigService {

    @Autowired
    private WebProxyConfigRepository configRepository;

    /**
     * 获取所有代理配置的水印设置
     */
    public List<WebProxyConfig> getAllWatermarkConfigs() {
        return configRepository.findAll();
    }

    /**
     * 根据代理名称获取水印配置
     */
    public WebProxyConfig getWatermarkConfigByProxyName(String proxyName) {
        return configRepository.findByProxyName(proxyName).orElse(null);
    }

    /**
     * 更新暗水印配置
     */
    @Transactional
    public WebProxyConfig updateInvisibleWatermarkConfig(Long configId, 
                                                        boolean enableInvisible,
                                                        String encodingStrength,
                                                        Double embedDensity) {
        WebProxyConfig config = configRepository.findById(configId)
            .orElseThrow(() -> new RuntimeException("配置不存在: " + configId));

        config.setEnableApiIntercept(enableInvisible);
        config.setInvisibleEncodingStrength(encodingStrength);
        config.setInvisibleEmbedDensity(embedDensity);

        WebProxyConfig saved = configRepository.save(config);
        log.info("更新暗水印配置 - 代理: {}, 启用: {}, 强度: {}, 密度: {}", 
                config.getProxyName(), enableInvisible, encodingStrength, embedDensity);

        return saved;
    }

    /**
     * 批量启用/禁用暗水印
     */
    @Transactional
    public int batchUpdateInvisibleWatermark(List<Long> configIds, boolean enabled) {
        int updatedCount = 0;
        for (Long configId : configIds) {
            try {
                WebProxyConfig config = configRepository.findById(configId).orElse(null);
                if (config != null) {
                    config.setEnableApiIntercept(enabled);
                    configRepository.save(config);
                    updatedCount++;
                }
            } catch (Exception e) {
                log.error("批量更新配置失败: {}", configId, e);
            }
        }
        
        log.info("批量更新暗水印配置完成 - 成功: {}/{}", updatedCount, configIds.size());
        return updatedCount;
    }

    /**
     * 获取水印配置统计信息
     */
    public Map<String, Object> getWatermarkStatistics() {
        List<WebProxyConfig> allConfigs = configRepository.findAll();
        
        Map<String, Object> stats = new HashMap<>();
        
        long totalConfigs = allConfigs.size();
        long pageWatermarkEnabled = allConfigs.stream()
            .mapToLong(c -> Boolean.TRUE.equals(c.getEnablePageWatermark()) ? 1 : 0)
            .sum();
        long apiWatermarkEnabled = allConfigs.stream()
            .mapToLong(c -> Boolean.TRUE.equals(c.getEnableApiWatermark()) ? 1 : 0)
            .sum();


        // 编码强度统计
        Map<String, Long> strengthStats = new HashMap<>();
        allConfigs.stream()
            .filter(c -> Boolean.TRUE.equals(c.getEnableApiWatermark()))
            .forEach(c -> {
                String strength = c.getInvisibleEncodingStrength();
                if (strength == null) strength = "medium";
                strengthStats.merge(strength, 1L, Long::sum);
            });

        stats.put("totalConfigs", totalConfigs);
        stats.put("pageWatermarkEnabled", pageWatermarkEnabled);
        stats.put("apiWatermarkEnabled", apiWatermarkEnabled);
        stats.put("strengthDistribution", strengthStats);
        
        return stats;
    }

    /**
     * 验证暗水印配置
     */
    public Map<String, Object> validateInvisibleWatermarkConfig(String encodingStrength, 
                                                               Double embedDensity) {
        Map<String, Object> result = new HashMap<>();
        boolean isValid = true;
        StringBuilder errors = new StringBuilder();

        // 验证编码强度
        if (encodingStrength == null || 
            (!encodingStrength.equals("low") && 
             !encodingStrength.equals("medium") && 
             !encodingStrength.equals("high"))) {
            isValid = false;
            errors.append("编码强度必须是 low、medium 或 high; ");
        }

        // 验证嵌入密度
        if (embedDensity == null || embedDensity < 0.1 || embedDensity > 1.0) {
            isValid = false;
            errors.append("嵌入密度必须在 0.1 到 1.0 之间; ");
        }

        result.put("isValid", isValid);
        result.put("errors", errors.toString());
        
        if (isValid) {
            result.put("message", "配置验证通过");
            
            // 提供配置建议
            Map<String, String> recommendations = new HashMap<>();
            if ("high".equals(encodingStrength)) {
                recommendations.put("performance", "高强度编码可能影响性能，建议在高安全要求场景使用");
            }
            if (embedDensity > 0.7) {
                recommendations.put("detection", "高嵌入密度可能增加被检测的风险");
            }
            if (embedDensity < 0.2) {
                recommendations.put("reliability", "低嵌入密度可能影响溯源可靠性");
            }
            
            result.put("recommendations", recommendations);
        }

        return result;
    }

    /**
     * 重置暗水印配置为默认值
     */
    @Transactional
    public WebProxyConfig resetInvisibleWatermarkConfig(Long configId) {
        WebProxyConfig config = configRepository.findById(configId)
            .orElseThrow(() -> new RuntimeException("配置不存在: " + configId));

        config.setEnableApiWatermark(true);
        config.setInvisibleEncodingStrength("medium");
        config.setInvisibleEmbedDensity(0.3);

        WebProxyConfig saved = configRepository.save(config);
        log.info("重置暗水印配置为默认值 - 代理: {}", config.getProxyName());

        return saved;
    }

    /**
     * 复制水印配置
     */
    @Transactional
    public void copyWatermarkConfig(Long sourceConfigId, List<Long> targetConfigIds) {
        WebProxyConfig sourceConfig = configRepository.findById(sourceConfigId)
            .orElseThrow(() -> new RuntimeException("源配置不存在: " + sourceConfigId));

        for (Long targetId : targetConfigIds) {
            try {
                WebProxyConfig targetConfig = configRepository.findById(targetId).orElse(null);
                if (targetConfig != null && !targetId.equals(sourceConfigId)) {
                    // 复制水印相关配置
                    targetConfig.setWatermarkText(sourceConfig.getWatermarkText());
                    targetConfig.setEnablePageWatermark(sourceConfig.getEnablePageWatermark());
                    targetConfig.setEnableApiWatermark(sourceConfig.getEnableApiWatermark());
                    targetConfig.setInvisibleEncodingStrength(sourceConfig.getInvisibleEncodingStrength());
                    targetConfig.setInvisibleEmbedDensity(sourceConfig.getInvisibleEmbedDensity());
                    targetConfig.setApiPathPatterns(sourceConfig.getApiPathPatterns());
                    
                    configRepository.save(targetConfig);
                    log.info("复制水印配置 - 从 {} 到 {}", sourceConfig.getProxyName(), targetConfig.getProxyName());
                }
            } catch (Exception e) {
                log.error("复制配置失败: {}", targetId, e);
            }
        }
    }

    /**
     * 获取推荐的水印配置
     */
    public Map<String, Object> getRecommendedConfig(String securityLevel) {
        Map<String, Object> config = new HashMap<>();
        
        switch (securityLevel.toLowerCase()) {
            case "high":
                config.put("enablePageWatermark", true);
                config.put("enableApiWatermark", true);
                config.put("enableInvisibleWatermark", true);
                config.put("invisibleEncodingStrength", "high");
                config.put("invisibleEmbedDensity", 0.8);
                config.put("description", "高安全级别：启用所有水印功能，最大化溯源能力");
                break;
                
            case "medium":
                config.put("enablePageWatermark", true);
                config.put("enableApiWatermark", true);
                config.put("enableInvisibleWatermark", true);
                config.put("invisibleEncodingStrength", "medium");
                config.put("invisibleEmbedDensity", 0.5);
                config.put("description", "中等安全级别：平衡安全性和性能");
                break;
                
            case "low":
                config.put("enablePageWatermark", false);
                config.put("enableApiWatermark", true);
                config.put("enableInvisibleWatermark", true);
                config.put("invisibleEncodingStrength", "low");
                config.put("invisibleEmbedDensity", 0.3);
                config.put("description", "低安全级别：最小化性能影响，保持基本溯源能力");
                break;
                
            default:
                config.put("enablePageWatermark", true);
                config.put("enableApiWatermark", true);
                config.put("enableInvisibleWatermark", true);
                config.put("invisibleEncodingStrength", "medium");
                config.put("invisibleEmbedDensity", 0.3);
                config.put("description", "默认配置：推荐的平衡设置");
        }
        
        return config;
    }
}
