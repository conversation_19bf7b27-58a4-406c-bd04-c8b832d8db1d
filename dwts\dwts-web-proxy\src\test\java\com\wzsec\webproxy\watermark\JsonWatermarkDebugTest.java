package com.wzsec.webproxy.watermark;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.impl.InvisibleWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * JSON水印加注调试测试
 *
 * <AUTHOR>
 * @date 2025/08/06
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class JsonWatermarkDebugTest {

    @Test
    public void testJsonContentTypeHandling() {
        InvisibleWatermarkProcessor processor = new InvisibleWatermarkProcessor();
        
        // 测试各种JSON内容类型
        String[] jsonContentTypes = {
            "application/json",
            "application/json; charset=utf-8",
            "text/json",
            "text/json; charset=utf-8"
        };
        
        for (String contentType : jsonContentTypes) {
            boolean canHandle = processor.canHandle(contentType);
            log.info("内容类型 [{}] 是否可处理: {}", contentType, canHandle);
            assertTrue(canHandle, "应该能处理内容类型: " + contentType);
        }
    }

    @Test
    public void testJsonWatermarkProcessing() {
        InvisibleWatermarkProcessor processor = new InvisibleWatermarkProcessor();
        WebProxyConfig config = createMockConfig();
        HttpServletRequest request = createMockRequest();
        
        String jsonContent = "{\n" +
            "    \"code\": 200,\n" +
            "    \"message\": \"success\",\n" +
            "    \"data\": {\n" +
            "        \"id\": 1,\n" +
            "        \"name\": \"测试用户\",\n" +
            "        \"email\": \"<EMAIL>\"\n" +
            "    }\n" +
            "}";
        
        log.info("=== 开始JSON水印处理调试 ===");
        log.info("原始JSON内容: {}", jsonContent);
        
        // 测试处理器是否能处理JSON
        boolean canHandle = processor.canHandle("application/json");
        log.info("处理器是否能处理application/json: {}", canHandle);
        assertTrue(canHandle, "处理器应该能处理application/json");
        
        // 测试配置是否启用
        log.info("API水印是否启用: {}", config.getEnableApiWatermark());
        assertTrue(config.getEnableApiWatermark(), "API水印应该启用");
        
        // 进行水印处理
        byte[] result = processor.processWatermark(
            jsonContent.getBytes(StandardCharsets.UTF_8),
            "application/json",
            request,
            config
        );
        
        String resultString = new String(result, StandardCharsets.UTF_8);
        log.info("处理后JSON内容: {}", resultString);
        log.info("原始长度: {}, 处理后长度: {}", jsonContent.length(), resultString.length());
        
        // 验证是否有变化
        assertNotEquals(jsonContent.length(), resultString.length(), "处理后长度应该有变化");
        
        log.info("=== JSON水印处理调试完成 ===");
    }

    @Test
    public void testWatermarkConfigValidation() {
        WebProxyConfig config = createMockConfig();
        
        log.info("=== 水印配置验证 ===");
        log.info("代理名称: {}", config.getProxyName());
        log.info("API水印启用: {}", config.getEnableApiWatermark());
        log.info("编码强度: {}", config.getInvisibleEncodingStrength());
        log.info("嵌入密度: {}", config.getInvisibleEmbedDensity());
        log.info("水印文本: {}", config.getWatermarkText());
        
        assertNotNull(config.getProxyName(), "代理名称不应为空");
        assertTrue(config.getEnableApiWatermark(), "API水印应该启用");
        assertNotNull(config.getInvisibleEncodingStrength(), "编码强度不应为空");
        assertNotNull(config.getInvisibleEmbedDensity(), "嵌入密度不应为空");
        assertNotNull(config.getWatermarkText(), "水印文本不应为空");
    }

    private WebProxyConfig createMockConfig() {
        WebProxyConfig config = new WebProxyConfig();
        config.setProxyName("debug-test-proxy");
        config.setEnableApiWatermark(true);
        config.setInvisibleEncodingStrength("medium");
        config.setInvisibleEmbedDensity(0.3);
        config.setWatermarkText("DWTS调试水印");
        return config;
    }

    private HttpServletRequest createMockRequest() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpSession session = mock(HttpSession.class);
        
        when(request.getRemoteAddr()).thenReturn("*************");
        when(request.getHeader("X-Forwarded-For")).thenReturn(null);
        when(request.getHeader("X-Real-IP")).thenReturn(null);
        when(request.getRequestURI()).thenReturn("/api/debug/test");
        when(request.getSession()).thenReturn(session);
        when(session.getId()).thenReturn("debug-session-123");
        when(request.getHeader("Authorization")).thenReturn("Bearer debug-token");
        
        return request;
    }
}
