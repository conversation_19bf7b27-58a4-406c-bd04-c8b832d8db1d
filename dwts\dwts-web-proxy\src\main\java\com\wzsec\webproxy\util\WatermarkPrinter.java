package com.wzsec.webproxy.util;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.impl.InvisibleWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 水印打印工具
 * 用于在控制台打印水印前后的文字对比
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
public class WatermarkPrinter {

    private static final InvisibleWatermarkProcessor processor = new InvisibleWatermarkProcessor();

    /**
     * 字符串重复方法（兼容Java 8）
     */
    private static String repeatString(String str, int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 打印水印前后的文字对比
     */
    public static void printWatermarkComparison(String originalText, String contentType) {
        System.out.println("\n" + repeatString("=", 80));
        System.out.println("🔒 DWTS 暗水印演示 - 文字对比");
        System.out.println(repeatString("=", 80));

        try {
            // 创建模拟请求和配置
            HttpServletRequest mockRequest = createMockRequest();
            WebProxyConfig mockConfig = createMockConfig();

            // 处理水印
            byte[] watermarkedBytes = processor.processWatermark(
                    originalText.getBytes(StandardCharsets.UTF_8),
                    contentType,
                    mockRequest,
                    mockConfig
            );

            String watermarkedText = new String(watermarkedBytes, StandardCharsets.UTF_8);

            // 打印对比结果
            printComparison(originalText, watermarkedText, contentType);

        } catch (Exception e) {
            System.err.println("❌ 水印处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 打印详细对比信息
     */
    private static void printComparison(String original, String watermarked, String contentType) {
        System.out.println("\n📊 基本信息:");
        System.out.println("   内容类型: " + contentType);
        System.out.println("   原始长度: " + original.length() + " 字符");
        System.out.println("   水印长度: " + watermarked.length() + " 字符");
        System.out.println("   增加字符: " + (watermarked.length() - original.length()) + " 个零宽字符");

        System.out.println("\n📄 原始内容:");
        System.out.println("┌" + repeatString("─", 78) + "┐");
        printWithLineNumbers(original);
        System.out.println("└" + repeatString("─", 78) + "┘");

        System.out.println("\n🔒 加水印内容:");
        System.out.println("┌" + repeatString("─", 78) + "┐");
        printWithLineNumbers(watermarked);
        System.out.println("└" + repeatString("─", 78) + "┘");

        // 分析差异
        Map<String, Object> analysis = analyzeWatermarkDifference(original, watermarked);
        printAnalysis(analysis);

        // 显示零宽字符的可视化表示
        printZeroWidthVisualization(watermarked);

        // 显示Unicode表示
        printUnicodeRepresentation(watermarked);
    }

    /**
     * 带行号打印文本
     */
    private static void printWithLineNumbers(String text) {
        String[] lines = text.split("\n");
        for (int i = 0; i < lines.length; i++) {
            System.out.printf("│%3d │ %s%n", i + 1, lines[i]);
        }
        if (lines.length == 1 && !text.contains("\n")) {
            // 单行文本，直接显示
            System.out.printf("│  1 │ %s%n", text);
        }
    }

    /**
     * 打印分析结果
     */
    private static void printAnalysis(Map<String, Object> analysis) {
        System.out.println("\n🔍 差异分析:");
        
        @SuppressWarnings("unchecked")
        Map<Integer, String> insertedPositions = (Map<Integer, String>) analysis.get("insertedPositions");
        
        @SuppressWarnings("unchecked")
        Map<String, Integer> charTypeStats = (Map<String, Integer>) analysis.get("zeroWidthCharTypes");

        System.out.println("   插入字符总数: " + analysis.get("totalInsertedChars"));
        
        if (!insertedPositions.isEmpty()) {
            System.out.println("   插入位置详情:");
            insertedPositions.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> 
                            System.out.println("     位置 " + entry.getKey() + ": " + entry.getValue()));
        }

        if (!charTypeStats.isEmpty()) {
            System.out.println("   零宽字符类型统计:");
            charTypeStats.forEach((charType, count) -> 
                    System.out.println("     " + charType + ": " + count + " 个"));
        }
    }

    /**
     * 打印零宽字符可视化表示
     */
    private static void printZeroWidthVisualization(String text) {
        System.out.println("\n👁️ 零宽字符可视化 (用标签表示):");
        System.out.println("┌" + repeatString("─", 78) + "┐");
        
        StringBuilder visualization = new StringBuilder();
        for (char c : text.toCharArray()) {
            String charName = getZeroWidthCharName(c);
            if (charName != null) {
                visualization.append("[").append(charName).append("]");
            } else {
                visualization.append(c);
            }
        }

        System.out.printf("│ %s%n", visualization.toString());
        System.out.println("└" + repeatString("─", 78) + "┘");
    }

    /**
     * 打印Unicode表示
     */
    private static void printUnicodeRepresentation(String text) {
        System.out.println("\n🔢 Unicode 表示:");
        System.out.println("┌" + repeatString("─", 78) + "┐");
        
        StringBuilder unicode = new StringBuilder();
        for (char c : text.toCharArray()) {
            if (getZeroWidthCharName(c) != null || c > 127) {
                unicode.append("\\u").append(String.format("%04X", (int) c));
            } else {
                unicode.append(c);
            }
        }
        
        // 分行显示，避免过长
        String unicodeStr = unicode.toString();
        int lineLength = 76;
        for (int i = 0; i < unicodeStr.length(); i += lineLength) {
            int end = Math.min(i + lineLength, unicodeStr.length());
            System.out.printf("│ %s%n", unicodeStr.substring(i, end));
        }

        System.out.println("└" + repeatString("─", 78) + "┘");
    }

    /**
     * 分析水印差异
     */
    private static Map<String, Object> analyzeWatermarkDifference(String original, String watermarked) {
        Map<String, Object> analysis = new HashMap<>();
        Map<Integer, String> insertedChars = new HashMap<>();
        Map<String, Integer> charTypeStats = new HashMap<>();

        int originalIndex = 0;
        int watermarkedIndex = 0;

        while (originalIndex < original.length() && watermarkedIndex < watermarked.length()) {
            char originalChar = original.charAt(originalIndex);
            char watermarkedChar = watermarked.charAt(watermarkedIndex);

            if (originalChar == watermarkedChar) {
                originalIndex++;
                watermarkedIndex++;
            } else {
                String charName = getZeroWidthCharName(watermarkedChar);
                if (charName != null) {
                    insertedChars.put(watermarkedIndex, charName);
                    charTypeStats.merge(charName, 1, Integer::sum);
                    watermarkedIndex++;
                } else {
                    originalIndex++;
                    watermarkedIndex++;
                }
            }
        }

        // 处理末尾的插入字符
        while (watermarkedIndex < watermarked.length()) {
            char watermarkedChar = watermarked.charAt(watermarkedIndex);
            String charName = getZeroWidthCharName(watermarkedChar);
            if (charName != null) {
                insertedChars.put(watermarkedIndex, charName);
                charTypeStats.merge(charName, 1, Integer::sum);
            }
            watermarkedIndex++;
        }

        analysis.put("insertedPositions", insertedChars);
        analysis.put("totalInsertedChars", insertedChars.size());
        analysis.put("zeroWidthCharTypes", charTypeStats);

        return analysis;
    }

    /**
     * 获取零宽字符名称
     */
    private static String getZeroWidthCharName(char c) {
        switch (c) {
            case '\u200B': return "ZERO_WIDTH_SPACE";
            case '\u200C': return "ZERO_WIDTH_NON_JOINER";
            case '\u200D': return "ZERO_WIDTH_JOINER";
            case '\u2060': return "WORD_JOINER";
            case '\u2062': return "INVISIBLE_SEPARATOR";
            default: return null;
        }
    }

    /**
     * 创建模拟HTTP请求
     */
    private static HttpServletRequest createMockRequest() {
        return new HttpServletRequest() {
            private final Map<String, Object> attributes = new HashMap<>();
            private final HttpSession session = createMockSession();

            @Override
            public String getRemoteAddr() {
                return "192.168.1.100";
            }

            @Override
            public String getRequestURI() {
                return "/api/demo";
            }

            @Override
            public HttpSession getSession() {
                return session;
            }

            @Override
            public HttpSession getSession(boolean create) {
                return session;
            }

            // 其他必需的方法实现（简化版）
            @Override public String getAuthType() { return null; }
            @Override public javax.servlet.http.Cookie[] getCookies() { return new javax.servlet.http.Cookie[0]; }
            @Override public long getDateHeader(String name) { return -1; }
            @Override public String getHeader(String name) { return null; }
            @Override public java.util.Enumeration<String> getHeaders(String name) { return java.util.Collections.emptyEnumeration(); }
            @Override public java.util.Enumeration<String> getHeaderNames() { return java.util.Collections.emptyEnumeration(); }
            @Override public int getIntHeader(String name) { return -1; }
            @Override public String getMethod() { return "GET"; }
            @Override public String getPathInfo() { return null; }
            @Override public String getPathTranslated() { return null; }
            @Override public String getContextPath() { return ""; }
            @Override public String getQueryString() { return null; }
            @Override public String getRemoteUser() { return "demo_user"; }
            @Override public boolean isUserInRole(String role) { return false; }
            @Override public java.security.Principal getUserPrincipal() { return null; }
            @Override public String getRequestedSessionId() { return session.getId(); }
            @Override public StringBuffer getRequestURL() { return new StringBuffer("http://localhost:8080/api/demo"); }
            @Override public String getServletPath() { return "/api/demo"; }
            @Override public boolean isRequestedSessionIdValid() { return true; }
            @Override public boolean isRequestedSessionIdFromCookie() { return true; }
            @Override public boolean isRequestedSessionIdFromURL() { return false; }
            @Override public boolean isRequestedSessionIdFromUrl() { return false; }
            @Override public boolean authenticate(javax.servlet.http.HttpServletResponse response) { return false; }
            @Override public void login(String username, String password) {}
            @Override public void logout() {}
            @Override public java.util.Collection<javax.servlet.http.Part> getParts() { return java.util.Collections.emptyList(); }
            @Override public javax.servlet.http.Part getPart(String name) { return null; }
            @Override public <T extends javax.servlet.http.HttpUpgradeHandler> T upgrade(Class<T> handlerClass) { return null; }
            @Override public String changeSessionId() { return session.getId(); }
            @Override public Object getAttribute(String name) { return attributes.get(name); }
            @Override public java.util.Enumeration<String> getAttributeNames() { return java.util.Collections.enumeration(attributes.keySet()); }
            @Override public String getCharacterEncoding() { return "UTF-8"; }
            @Override public void setCharacterEncoding(String env) {}
            @Override public int getContentLength() { return -1; }
            @Override public long getContentLengthLong() { return -1; }
            @Override public String getContentType() { return "application/json"; }
            @Override public javax.servlet.ServletInputStream getInputStream() { return null; }
            @Override public String getParameter(String name) { return null; }
            @Override public java.util.Enumeration<String> getParameterNames() { return java.util.Collections.emptyEnumeration(); }
            @Override public String[] getParameterValues(String name) { return new String[0]; }
            @Override public java.util.Map<String, String[]> getParameterMap() { return java.util.Collections.emptyMap(); }
            @Override public String getProtocol() { return "HTTP/1.1"; }
            @Override public String getScheme() { return "http"; }
            @Override public String getServerName() { return "localhost"; }
            @Override public int getServerPort() { return 8080; }
            @Override public java.io.BufferedReader getReader() { return null; }
            @Override public String getRemoteHost() { return "localhost"; }
            @Override public void setAttribute(String name, Object o) { attributes.put(name, o); }
            @Override public void removeAttribute(String name) { attributes.remove(name); }
            @Override public java.util.Locale getLocale() { return java.util.Locale.getDefault(); }
            @Override public java.util.Enumeration<java.util.Locale> getLocales() { return java.util.Collections.enumeration(java.util.Arrays.asList(java.util.Locale.getDefault())); }
            @Override public boolean isSecure() { return false; }
            @Override public javax.servlet.RequestDispatcher getRequestDispatcher(String path) { return null; }
            @Override public String getRealPath(String path) { return null; }
            @Override public int getRemotePort() { return 0; }
            @Override public String getLocalName() { return "localhost"; }
            @Override public String getLocalAddr() { return "127.0.0.1"; }
            @Override public int getLocalPort() { return 8080; }
            @Override public javax.servlet.ServletContext getServletContext() { return null; }
            @Override public javax.servlet.AsyncContext startAsync() { return null; }
            @Override public javax.servlet.AsyncContext startAsync(javax.servlet.ServletRequest servletRequest, javax.servlet.ServletResponse servletResponse) { return null; }
            @Override public boolean isAsyncStarted() { return false; }
            @Override public boolean isAsyncSupported() { return false; }
            @Override public javax.servlet.AsyncContext getAsyncContext() { return null; }
            @Override public javax.servlet.DispatcherType getDispatcherType() { return javax.servlet.DispatcherType.REQUEST; }
        };
    }

    /**
     * 创建模拟Session
     */
    private static HttpSession createMockSession() {
        return new HttpSession() {
            private final Map<String, Object> attributes = new HashMap<>();
            private final String sessionId = "MOCK_SESSION_" + System.currentTimeMillis();

            {
                attributes.put("username", "demo_user");
            }

            @Override public long getCreationTime() { return System.currentTimeMillis(); }
            @Override public String getId() { return sessionId; }
            @Override public long getLastAccessedTime() { return System.currentTimeMillis(); }
            @Override public javax.servlet.ServletContext getServletContext() { return null; }
            @Override public void setMaxInactiveInterval(int interval) {}
            @Override public int getMaxInactiveInterval() { return 1800; }
            @Override public javax.servlet.http.HttpSessionContext getSessionContext() { return null; }
            @Override public Object getAttribute(String name) { return attributes.get(name); }
            @Override public Object getValue(String name) { return getAttribute(name); }
            @Override public java.util.Enumeration<String> getAttributeNames() { return java.util.Collections.enumeration(attributes.keySet()); }
            @Override public String[] getValueNames() { return attributes.keySet().toArray(new String[0]); }
            @Override public void setAttribute(String name, Object value) { attributes.put(name, value); }
            @Override public void putValue(String name, Object value) { setAttribute(name, value); }
            @Override public void removeAttribute(String name) { attributes.remove(name); }
            @Override public void removeValue(String name) { removeAttribute(name); }
            @Override public void invalidate() { attributes.clear(); }
            @Override public boolean isNew() { return false; }
        };
    }

    /**
     * 创建模拟配置
     */
    private static WebProxyConfig createMockConfig() {
        WebProxyConfig config = new WebProxyConfig();
        config.setProxyName("demo-proxy");
        config.setEnableApiWatermark(true);
        config.setInvisibleEncodingStrength("medium");
        config.setInvisibleEmbedDensity(0.3);
        config.setWatermarkText("DWTS演示水印");
        return config;
    }

    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
        // 测试JSON内容
        String jsonContent = "{\"users\":[{\"id\":1,\"name\":\"张三\",\"email\":\"<EMAIL>\"},{\"id\":2,\"name\":\"李四\",\"email\":\"<EMAIL>\"}]}";
        printWatermarkComparison(jsonContent, "application/json");

        System.out.println("\n" + repeatString("=", 80));
        
        // 测试纯文本内容
        String textContent = "这是一段测试文本，用于演示暗水印技术的效果。";
        printWatermarkComparison(textContent, "text/plain");
    }
}
