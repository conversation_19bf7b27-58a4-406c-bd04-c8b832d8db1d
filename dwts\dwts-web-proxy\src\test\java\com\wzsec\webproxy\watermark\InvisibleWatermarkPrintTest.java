package com.wzsec.webproxy.watermark;

import com.wzsec.webproxy.domain.WebProxyConfig;
import com.wzsec.webproxy.watermark.impl.InvisibleWatermarkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * API水印（暗水印）打印功能测试
 *
 * <AUTHOR>
 * @date 2025/08/06
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class InvisibleWatermarkPrintTest {

    @Test
    public void testJsonWatermarkPrint() {
        // 创建处理器
        InvisibleWatermarkProcessor processor = new InvisibleWatermarkProcessor();
        
        // 创建模拟配置
        WebProxyConfig config = createMockConfig();
        
        // 创建模拟请求
        HttpServletRequest request = createMockRequest();
        
        // 测试JSON内容
        String jsonContent = "{\n" +
            "    \"code\": 200,\n" +
            "    \"message\": \"success\",\n" +
            "    \"data\": [\n" +
            "        {\"id\": 1, \"name\": \"张三\", \"email\": \"<EMAIL>\"},\n" +
            "        {\"id\": 2, \"name\": \"李四\", \"email\": \"<EMAIL>\"}\n" +
            "    ],\n" +
            "    \"total\": 2\n" +
            "}";
        
        log.info("开始测试JSON水印打印功能");
        
        // 处理水印
        byte[] result = processor.processWatermark(
                jsonContent.getBytes(StandardCharsets.UTF_8),
                "application/json",
                request,
                config
        );
        
        log.info("JSON水印测试完成，结果长度: {} bytes", result.length);
    }

    @Test
    public void testNonDataContentShouldPassThrough() {
        // 创建处理器
        InvisibleWatermarkProcessor processor = new InvisibleWatermarkProcessor();

        // 测试图片内容类型
        assertFalse(processor.canHandle("image/jpeg"), "图片内容应该直接放行");
        assertFalse(processor.canHandle("image/png"), "PNG图片应该直接放行");

        // 测试视频内容类型
        assertFalse(processor.canHandle("video/mp4"), "视频内容应该直接放行");

        // 测试文档内容类型
        assertFalse(processor.canHandle("application/pdf"), "PDF文档应该直接放行");
        assertFalse(processor.canHandle("application/msword"), "Word文档应该直接放行");

        // 测试压缩文件
        assertFalse(processor.canHandle("application/zip"), "ZIP文件应该直接放行");

        // 测试二进制文件
        assertFalse(processor.canHandle("application/octet-stream"), "二进制文件应该直接放行");

        // 测试HTML内容（应该由页面水印处理器处理）
        assertFalse(processor.canHandle("text/html"), "HTML内容应该由页面水印处理器处理");

        log.info("非数据接口内容类型验证通过，这些内容将直接放行");
    }

    @Test
    public void testDataInterfaceContentShouldBeProcessed() {
        // 创建处理器
        InvisibleWatermarkProcessor processor = new InvisibleWatermarkProcessor();

        // 测试JSON内容类型
        assertTrue(processor.canHandle("application/json"), "JSON内容应该进行暗水印处理");
        assertTrue(processor.canHandle("text/json"), "JSON文本应该进行暗水印处理");

        // 测试XML内容类型
        assertTrue(processor.canHandle("application/xml"), "XML内容应该进行暗水印处理");
        assertTrue(processor.canHandle("text/xml"), "XML文本应该进行暗水印处理");

        // 测试CSV内容类型
        assertTrue(processor.canHandle("text/csv"), "CSV内容应该进行暗水印处理");
        assertTrue(processor.canHandle("application/csv"), "CSV应用类型应该进行暗水印处理");

        // 测试纯文本数据
        assertTrue(processor.canHandle("text/plain"), "纯文本数据应该进行暗水印处理");

        // 测试JSONL
        assertTrue(processor.canHandle("application/x-ndjson"), "JSONL内容应该进行暗水印处理");
        assertTrue(processor.canHandle("application/jsonl"), "JSONL应该进行暗水印处理");

        log.info("数据接口内容类型验证通过，这些内容将进行暗水印处理");
    }

    @Test
    public void testXmlWatermarkPrint() {
        // 创建处理器
        InvisibleWatermarkProcessor processor = new InvisibleWatermarkProcessor();
        
        // 创建模拟配置
        WebProxyConfig config = createMockConfig();
        
        // 创建模拟请求
        HttpServletRequest request = createMockRequest();
        
        // 测试XML内容
        String xmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <response>
                <code>200</code>
                <message>success</message>
                <data>
                    <item>
                        <id>1</id>
                        <name>张三</name>
                        <email><EMAIL></email>
                    </item>
                    <item>
                        <id>2</id>
                        <name>李四</name>
                        <email><EMAIL></email>
                    </item>
                </data>
                <total>2</total>
            </response>
            """;
        
        log.info("开始测试XML水印打印功能");
        
        // 处理水印
        byte[] result = processor.processWatermark(
                xmlContent.getBytes(StandardCharsets.UTF_8),
                "application/xml",
                request,
                config
        );
        
        log.info("XML水印测试完成，结果长度: {} bytes", result.length);
    }

    private WebProxyConfig createMockConfig() {
        WebProxyConfig config = new WebProxyConfig();
        config.setProxyName("test-proxy");
        config.setEnableApiWatermark(true);
        config.setInvisibleEncodingStrength("medium");
        config.setInvisibleEmbedDensity(0.3);
        config.setWatermarkText("DWTS测试水印");
        return config;
    }

    private HttpServletRequest createMockRequest() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpSession session = mock(HttpSession.class);
        
        when(request.getRemoteAddr()).thenReturn("*************");
        when(request.getHeader("X-Forwarded-For")).thenReturn(null);
        when(request.getHeader("X-Real-IP")).thenReturn(null);
        when(request.getRequestURI()).thenReturn("/api/test");
        when(request.getSession()).thenReturn(session);
        when(session.getId()).thenReturn("test-session-123");
        when(request.getHeader("Authorization")).thenReturn("Bearer test-token");
        
        return request;
    }
}
