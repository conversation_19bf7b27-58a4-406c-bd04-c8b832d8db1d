server:
  port: 8080

spring:
  datasource:
    url: jdbc:mysql://*************:3306/dwts-clean?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
    username: root
    password: <EMAIL>
    
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        format_sql: true

# 日志配置
logging:
  level:
    com.wzsec: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 代理配置
web-proxy:
  rest-template:
    connect-timeout: 5000
    read-timeout: 30000
    max-connections: 100
    max-connections-per-route: 20

# 自定义配置
dwts:
  web-proxy:
    enable-request-logging: true
    enable-response-logging: true
