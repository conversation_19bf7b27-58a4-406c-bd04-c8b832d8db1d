package com.wzsec.webproxy.watermark;

import com.wzsec.webproxy.util.WatermarkDebugUtil;
import com.wzsec.webproxy.watermark.util.WatermarkExtractor;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;

/**
 * 水印检测测试
 *
 * <AUTHOR>
 * @date 2025/08/06
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class WatermarkDetectionTest {

    @Test
    public void testDetectWatermarkInProvidedText() {
        // 您提供的包含水印的文本
        String watermarkedText = "⁢B‌a‌c‌k‌e‌n‌d​(⁠S‌y​s‌t‌e‌m‌)​ ‍s‌e⁠r⁠v​i​c⁠e​ ‌s​t⁠a‍r‌t​e⁠d​ ‍s​u‍c⁠c‍e​s⁠s​f‌u​l⁠l‌y";
        
        log.info("=== 测试检测用户提供的水印文本 ===");
        log.info("文本内容: {}", watermarkedText);
        log.info("文本长度: {} 字符", watermarkedText.length());
        
        // 使用调试工具分析
        WatermarkDebugUtil debugUtil = new WatermarkDebugUtil();
        Map<String, Object> analysis = debugUtil.fullAnalysis(watermarkedText);
        
        log.info("分析结果: {}", analysis);
        
        // 使用提取器测试
        WatermarkExtractor extractor = new WatermarkExtractor();
        var watermarks = extractor.extractWatermarks(watermarkedText, "text/plain");
        
        log.info("提取到的水印数量: {}", watermarks.size());
        for (int i = 0; i < watermarks.size(); i++) {
            var watermark = watermarks.get(i);
            log.info("水印 {}: 位置={}, 信息={}", i + 1, watermark.getPosition(), watermark.getWatermarkInfo());
        }
        
        log.info("=== 测试完成 ===");
    }

    @Test
    public void testAnalyzeZeroWidthChars() {
        String watermarkedText = "⁢B‌a‌c‌k‌e‌n‌d​(⁠S‌y​s‌t‌e‌m‌)​ ‍s‌e⁠r⁠v​i​c⁠e​ ‌s​t⁠a‍r‌t​e⁠d​ ‍s​u‍c⁠c‍e​s⁠s​f‌u​l⁠l‌y";
        
        log.info("=== 分析零宽字符 ===");
        
        // 逐字符分析
        for (int i = 0; i < watermarkedText.length(); i++) {
            char c = watermarkedText.charAt(i);
            String unicode = String.format("\\u%04X", (int) c);
            String charName = getZeroWidthCharName(c);
            
            if (charName != null) {
                log.info("位置 {}: {} ({})", i, charName, unicode);
            } else if (c < 32 || c > 126) {
                log.info("位置 {}: 特殊字符 '{}' ({})", i, c, unicode);
            } else {
                log.debug("位置 {}: 普通字符 '{}'", i, c);
            }
        }
        
        log.info("=== 分析完成 ===");
    }

    private String getZeroWidthCharName(char c) {
        switch (c) {
            case '\u200B': return "ZERO_WIDTH_SPACE";
            case '\u200C': return "ZERO_WIDTH_NON_JOINER";
            case '\u200D': return "ZERO_WIDTH_JOINER";
            case '\u2060': return "WORD_JOINER";
            case '\u2062': return "INVISIBLE_SEPARATOR";
            case '\u2063': return "INVISIBLE_TIMES";
            case '\u2064': return "INVISIBLE_PLUS";
            default: return null;
        }
    }

    @Test
    public void testSimpleWatermarkGeneration() {
        log.info("=== 测试简单水印生成和检测 ===");
        
        // 创建一个简单的测试文本
        String originalText = "Backend(System) service started successfully";
        log.info("原始文本: {}", originalText);
        
        // 这里应该调用水印处理器生成水印，然后测试检测
        // 由于需要完整的Spring上下文，这里只是演示框架
        
        log.info("=== 测试完成 ===");
    }
}
